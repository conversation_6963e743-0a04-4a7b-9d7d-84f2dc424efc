{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"build": "echo \"build step\"", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/server.js", "db": "node scripts/initialize-database.js", "superadmin": "node scripts/create-super-admin.js", "admin": "node scripts/create-admin.js", "migrate:google-calendar": "node scripts/add-google-calendar-fields.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "googleapis": "^140.0.0", "google-auth-library": "^10.0.0", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "pg": "^8.16.2", "sequelize": "^6.37.7"}}