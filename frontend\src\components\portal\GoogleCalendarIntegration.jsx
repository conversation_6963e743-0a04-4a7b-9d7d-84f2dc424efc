import React, { useState, useEffect } from 'react';
import { googleCalendarApi } from '../../api/googleCalendar';
import toast from 'react-hot-toast';

export default function GoogleCalendarIntegration({ providerId }) {
  const [calendarStatus, setCalendarStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [testing, setTesting] = useState(false);

  // Get provider ID from logged-in user if not passed as prop
  const userId = providerId || JSON.parse(localStorage.getItem('portal_authed_user'))?.id;

  useEffect(() => {
    if (userId) {
      fetchCalendarStatus();
      
      // Clean up stale OAuth pending states (older than 1 hour)
      const cleanupStaleOAuth = () => {
        const oauthTimestamp = localStorage.getItem('google_oauth_timestamp');
        if (oauthTimestamp) {
          const timeDiff = Date.now() - parseInt(oauthTimestamp);
          const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
          
          if (timeDiff > oneHour) {
            localStorage.removeItem('google_oauth_pending');
            localStorage.removeItem('google_oauth_timestamp');
          }
        }
      };
      
      cleanupStaleOAuth();
    }
  }, [userId]);

  const fetchCalendarStatus = async () => {
    try {
      setLoading(true);
      const response = await googleCalendarApi.getCalendarStatus(userId);
      setCalendarStatus(response.provider);
      
      // If we successfully got status and user is connected, clear pending state
      if (response.provider?.google_calendar_connected) {
        localStorage.removeItem('google_oauth_pending');
        localStorage.removeItem('google_oauth_timestamp');
      }
    } catch (error) {
      console.error('Failed to fetch calendar status:', error);
      toast.error('Failed to load calendar status');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectCalendar = async () => {
    try {
      setConnecting(true);
      const response = await googleCalendarApi.startOAuth(userId);
      
      // Store the OAuth state for verification
      localStorage.setItem('google_oauth_pending', 'true');
      localStorage.setItem('google_oauth_timestamp', Date.now().toString());
      
      // Open OAuth URL in new window/tab
      // The OAuth URL from backend will redirect to the backend callback endpoint
      const authWindow = window.open(
        response.authUrl,
        'google-oauth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      // Show instructions to user
      toast.success('Google OAuth window opened! Complete the authorization and then click "Refresh Status" below.', {
        duration: 8000,
      });

      // Don't try to poll window.closed due to COOP restrictions
      // User will manually refresh status after completing OAuth
      setConnecting(false);
      
    } catch (error) {
      console.error('Failed to start OAuth flow:', error);
      toast.error('Failed to connect Google Calendar');
      setConnecting(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTesting(true);
      const response = await googleCalendarApi.testCalendarConnection(userId);
      toast.success(response.message);
      await fetchCalendarStatus(); // Refresh status
    } catch (error) {
      console.error('Calendar connection test failed:', error);
      toast.error(error.response?.data?.message || 'Connection test failed');
    } finally {
      setTesting(false);
    }
  };

  const handleDisconnectCalendar = async () => {
    if (!window.confirm('Are you sure you want to disconnect Google Calendar? This will stop automatic appointment syncing.')) {
      return;
    }

    try {
      await googleCalendarApi.disconnectCalendar(userId);
      toast.success('Google Calendar disconnected successfully');
      await fetchCalendarStatus(); // Refresh status
    } catch (error) {
      console.error('Failed to disconnect calendar:', error);
      toast.error('Failed to disconnect calendar');
    }
  };

  const handleManualRefresh = async () => {
    await fetchCalendarStatus();
    toast.success('Status refreshed!');
  };

  const handleCancelAuthorization = () => {
    if (window.confirm('Cancel the pending Google Calendar authorization? This will reset the connection process.')) {
      localStorage.removeItem('google_oauth_pending');
      localStorage.removeItem('google_oauth_timestamp');
      toast.success('Authorization cancelled. You can start over by clicking "Connect Google Calendar".');
      // Force re-render by updating state
      setCalendarStatus(prev => ({ ...prev }));
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  const isConnected = calendarStatus?.google_calendar_connected;
  const hasOAuthPending = localStorage.getItem('google_oauth_pending') === 'true';

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            Google Calendar Integration
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Connect your Google Calendar to automatically sync appointments
          </p>
        </div>
        
        {/* Status Badge */}
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
          isConnected 
            ? 'bg-green-100 text-green-800' 
            : hasOAuthPending
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {isConnected ? 'Connected' : hasOAuthPending ? 'Authorization Pending' : 'Not Connected'}
        </div>
      </div>

      {/* Connection Status */}
      <div className="mb-6">
        {isConnected ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-sm font-medium text-green-800">
                  Google Calendar is connected and syncing appointments
                </p>
                {calendarStatus?.google_calendar_expiry && (
                  <p className="text-xs text-green-600 mt-1">
                    Token expires: {new Date(calendarStatus.google_calendar_expiry).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        ) : hasOAuthPending ? (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Google OAuth authorization is pending
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  Complete the authorization in the popup window, then click "Refresh Status" below
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  If you closed the popup window, you can cancel this authorization and start over
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-gray-600">
                Connect your Google Calendar to automatically sync appointments when they're created, updated, or deleted.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        {!isConnected && !hasOAuthPending ? (
          <button
            onClick={handleConnectCalendar}
            disabled={connecting}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {connecting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Opening OAuth...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                </svg>
                Connect Google Calendar
              </>
            )}
          </button>
        ) : hasOAuthPending ? (
          <div className="flex gap-3">
            <button
              onClick={handleManualRefresh}
              className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
              Refresh Status
            </button>
            
            <button
              onClick={handleCancelAuthorization}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Cancel Authorization
            </button>
          </div>
        ) : (
          <>
            <button
              onClick={handleTestConnection}
              disabled={testing}
              className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {testing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Testing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clipRule="evenodd" />
                  </svg>
                  Test Connection
                </>
              )}
            </button>

            <button
              onClick={handleDisconnectCalendar}
              className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Disconnect
            </button>
          </>
        )}
      </div>

      {/* OAuth Instructions */}
      {hasOAuthPending && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Next Steps:</h4>
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Complete the Google authorization in the popup window</li>
            <li>Click "Refresh Status" above to check if connection was successful</li>
            <li>If successful, the status will change to "Connected"</li>
            <li><strong>If you closed the popup window:</strong> Click "Cancel Authorization" to reset and start over</li>
          </ol>
        </div>
      )}

      {/* Information */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">How it works:</h4>
        <ul className="text-sm text-gray-600 space-y-2">
          <li className="flex items-start gap-2">
            <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Appointments are automatically synced to your Google Calendar</span>
          </li>
          <li className="flex items-start gap-2">
            <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Changes to appointments (time, date, status) update your calendar</span>
          </li>
          <li className="flex items-start gap-2">
            <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Deleted appointments are removed from your calendar</span>
          </li>
          <li className="flex items-start gap-2">
            <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Access your appointments on any device through Google Calendar</span>
          </li>
        </ul>
      </div>
    </div>
  );
}
